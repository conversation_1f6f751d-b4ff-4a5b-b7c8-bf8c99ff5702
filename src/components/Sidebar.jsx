"use client"

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { cn, focusRing } from '@/lib/utils';
import { Button } from './ui/button';
import { ScrollArea } from './ui/scroll-area';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { RiMore2Fill } from "@remixicon/react";
import { menuItems } from '@/lib/menuitems';
import { DropdownUserProfile } from './DropdownUserProfile';
// Dummy menu data

const Sidebar = ({ isDrawerOpen, toggleDrawer }) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showTitle, setShowTitle] = useState(true);

  const { data: session } = useSession();

  useEffect(() => {
    if (!isSidebarCollapsed) {
      const timer = setTimeout(() => {
        setShowTitle(true);
      }, 150);
      return () => clearTimeout(timer);
    } else {
      setShowTitle(false);
    }
  }, [isSidebarCollapsed]);

  // Kullanıcının rolüne göre menü öğelerini filtrele ve section'lara göre grupla
  const getFilteredAndGroupedMenuItems = () => {
    const userRole = session?.user?.role || 'user';

    // Kullanıcının rolüne göre menü öğelerini filtrele
    const filteredItems = menuItems.filter(item => {
      return item.disabled == false && (item.roles.includes('all') || item.roles.includes(userRole));
    });

    // Section'lara göre grupla
    const groupedItems = filteredItems.reduce((acc, item) => {
      const sectionKey = item.section || 'main';

      if (!acc[sectionKey]) {
        acc[sectionKey] = {
          sectionKey,
          sectionTitle: item.sectionTitle || null,
          items: []
        };
      }

      acc[sectionKey].items.push(item);
      return acc;
    }, {});

    // Her section içindeki öğeleri weight'e göre sırala
    Object.values(groupedItems).forEach(section => {
      section.items.sort((a, b) => (a.weight || 0) - (b.weight || 0));
    });

    // Section'ları belirli bir sıraya göre döndür
    const sectionOrder = ['main', 'ms', 'products', 'others', 'admin'];

    return sectionOrder
      .filter(sectionKey => groupedItems[sectionKey])
      .map(sectionKey => groupedItems[sectionKey]);
  };

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };
  return (
    <>
      {/* Overlay for Drawer */}
      {isDrawerOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={toggleDrawer}
        ></div>
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          'flex flex-col bg-gray-50 transition-all duration-300 ease-in-out h-screen',
          // Desktop: always visible, no positioning classes needed (parent handles sticky)
          'hidden md:flex',
          isSidebarCollapsed ? 'w-20' : 'w-64',
          // Mobile: drawer with fixed positioning
          'md:relative fixed inset-y-0 left-0 z-50',
          isDrawerOpen ? 'translate-x-0' : '-translate-x-full',
          'md:translate-x-0'
        )}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <h1
            className={cn(
              'text-xl font-semibold text-gray-800 transition-opacity duration-150',
              showTitle ? 'opacity-100' : 'opacity-0',
              isSidebarCollapsed ? 'hidden' : ''
            )}
          >
            TTS Panel
          </h1>
          <Button variant="ghost" size="icon" onClick={toggleSidebar} className={cn(isSidebarCollapsed ? 'ml-auto' : '', 'hidden md:flex')}>
            {isSidebarCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
          </Button>
        </div>
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="space-y-4 px-2">
            {getFilteredAndGroupedMenuItems().map((section) => (
              <div key={section.sectionKey} className="space-y-1">
                {/* Section Title - sadece collapsed değilse göster */}
                {!isSidebarCollapsed && section.sectionTitle && section.sectionKey !== 'main' && (
                  <div className="px-3 py-2">
                    <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                      {section.sectionTitle}
                    </h3>
                  </div>
                )}

                {/* Section Items */}
                {section.items.map((item) => (
                  <Link key={item.id} href={item.href} passHref>
                    <Button
                      variant="ghost"
                      className={cn(
                        'w-full cursor-pointer',
                        !isSidebarCollapsed && 'hover:shadow-md',
                        isSidebarCollapsed ? 'justify-center px-0' : 'justify-start px-4'
                      )}
                      title={isSidebarCollapsed ? item.label : ''}
                    >
                      <span className={cn('h-8 w-8 flex items-center justify-center border border-gray-200 rounded-md', isSidebarCollapsed ? '' : 'mr-3')}>
                        <item.icon className={cn('h-5 w-5 text-blue-500', isSidebarCollapsed && 'hover:text-blue-800')} />
                      </span>
                      <span
                        className={cn(
                          'transition-opacity duration-300',
                          !isSidebarCollapsed && 'hover:text-blue-800',
                          isSidebarCollapsed ? 'opacity-0 hidden' : 'opacity-100'
                        )}
                      >
                        {item.label}
                      </span>
                    </Button>
                  </Link>
                ))}
              </div>
            ))}
          </nav>
        </div>

        <div className="flex-shrink-0 border-t border-gray-200 p-4">
          {/* Desktop User Profile */}
          {!isSidebarCollapsed && (
            <DropdownUserProfile>
              <Button
                aria-label="User settings"
                variant="ghost"
                className={cn(
                  "group flex w-full items-center justify-between rounded-md p-2 text-sm font-medium text-gray-900 hover:bg-gray-100 data-[state=open]:bg-gray-100 data-[state=open]:bg-gray-400/10 hover:dark:bg-gray-400/10",
                  focusRing,
                )}
              >
                <span className="flex items-center gap-3">
                  <img
                    className="h-8 w-8 rounded-full border border-gray-300"
                    src={session?.user?.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session?.user?.name || 'User')}&background=6366f1&color=fff`}
                    alt=""
                  />
                  <span className="text-left">
                    <p className="text-sm font-medium text-gray-700">{session?.user?.name}</p>
                    <p className="text-xs text-gray-500">{session?.user?.role}</p>
                  </span>
                </span>
                <RiMore2Fill
                  className="size-4 shrink-0 text-gray-500 group-hover:text-gray-700 group-hover:dark:text-gray-400"
                  aria-hidden="true"
                />
              </Button>
            </DropdownUserProfile>
          )}

          {/* Collapsed User Profile */}
          {isSidebarCollapsed && (
            <DropdownUserProfile align="end">
              <Button
                aria-label="User settings"
                variant="ghost"
                className={cn(
                  "group flex items-center justify-center rounded-md p-1 text-sm font-medium text-gray-900 hover:bg-gray-100 data-[state=open]:bg-gray-100 data-[state=open]:bg-gray-400/10 hover:dark:bg-gray-400/10 w-full",
                )}
              >
                <img
                  className="h-8 w-8 rounded-full border border-gray-300"
                  src={session?.user?.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session?.user?.name || 'User')}&background=6366f1&color=fff`}
                  alt=""
                />
              </Button>
            </DropdownUserProfile>
          )}
        </div>
      </aside>
    </>
  );
};

export default Sidebar;