/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const { decodeJwt } = require('jose');
const jwt = require('jsonwebtoken');
const { vars } = require('@/lib/constants');
const { sendVerificationEmail, sendWelcomeEmail } = require('./fnx.email'); // Bu modülü oluşturmanız gerekecek

// Yardımcı fonksiyonlar
const generateUniqueId = () => crypto.randomBytes(16).toString('hex');
const generateClientSchema = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    return Array.from({ length: 10 }, () => chars.charAt(Math.floor(Math.random() * chars.length))).join('').toLowerCase();
};

// TitleCase dönüşümü için yardımcı fonksiyon
const toTitleCase = (str) => {
    return str
      .toLowerCase()
      .split(' ')
      .map(word => {
        // Özel durumlar için kontrol (ve, veya, ile, of, in, at gibi bağlaçları küçük harfle bırak)
        const lowercaseWords = ['ve', 'veya', 'ile', 'of', 'in', 'at', 'the', 'and', 'or'];
        if (lowercaseWords.includes(word)) return word;
        return word.charAt(0).toUpperCase() + word.slice(1);
      })
      .join(' ');
  };

  
const toTitleCase_ = (str) => {
    return str.replace(/\b\w/g, (char) => char.toUpperCase());
};

exports.signIn = ({ dbConn, name, email, password }) => {
    return new Promise(async (resolve, reject) => {
        try {
            const db = await dbConn.db(vars.db.dbName);
            const users = db.collection(vars.db.collection.users);
            const customers = db.collection('tflouu.main.dim.customers');
            const inviteList = db.collection('tflouu.main.dim.customers.invitelist');
            
            const username = email ? email.toLowerCase() : null;
            if (!username || !password) {
                reject({ message: 'Email / Password is needed.' });
                return;
            }

            // Email kontrolü
            const existingUser = await users.findOne({ email: username });
            if (existingUser) {
                reject({ message: "User already exists" });
                return;
            }

            // Davet kontrolü
            const invitation = await inviteList.findOne({
                email: username,
                status: 0, // Aktif davet
                expireDate: { $gt: new Date() } // Süresi geçmemiş
            });

            const isInvited = !!invitation;

            // İsmi TitleCase formatına dönüştür
            const formattedName = toTitleCase(name);

            // Unique ID'ler oluştur
            const clientId = isInvited ? invitation.clientId : generateUniqueId();
            const clientSchema = isInvited ? invitation.clientSchema : generateClientSchema();
            const verificationToken = generateUniqueId();

            // Password hash
            const SALT_FACTOR = 5;
            const salt = await bcrypt.genSalt(SALT_FACTOR);
            const hashedPassword = await bcrypt.hash(password, salt);

            let customerResult;
            if (isInvited) {
                // Mevcut customer'ı kullan
                customerResult = { insertedId: invitation.customerId };
            } else {
                // Yeni customer oluştur
                customerResult = await customers.insertOne({
                    clientId,
                    clientSchema,
                    customerName: '',
                    adminName: formattedName,
                    verified: false,
                    subscriptionPlan: 'free',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    adminUserID: null,
                });

                if (!customerResult.insertedId) {
                    throw new Error('Failed to create customer record');
                }
            }

            // User oluştur
            const userResult = await users.insertOne({
                fullName: formattedName,
                email: username,
                password: hashedPassword,
                userType: 0,
                role: isInvited ? 'member' : 'adminActing',
                clientId,
                clientSchema,  // Eklenen alan
                customerId: customerResult.insertedId,
                token: '',
                verified: isInvited, // Davetli kullanıcılar otomatik verify
                verificationToken: isInvited ? null : verificationToken,
                isFirstAdmin: !isInvited, // Sadece normal kayıt olan kullanıcılar için true
                createdAt: new Date(),
                updatedAt: new Date()
            });

            if (!userResult.insertedId) {
                if (!isInvited) {
                    // Rollback customer creation (sadece yeni customer oluşturulduysa)
                    await customers.deleteOne({ _id: customerResult.insertedId });
                }
                throw new Error('Failed to create user record');
            }

            if (!isInvited) {
                // Customer'a adminUserID ekle (sadece yeni customer oluşturulduysa)
                await customers.updateOne(
                    { _id: customerResult.insertedId },
                    { $set: { adminUserID: userResult.insertedId } }
                );
            }

            // Send welcome email
            await sendWelcomeEmail({
                email: username,
                name: formattedName
            });
            
            // Verification email sadece davetli olmayan kullanıcılara gönder
            if (!isInvited) {
                await sendVerificationEmail({
                    email: username,
                    token: verificationToken,
                    name: formattedName
                });
            }

            // Eğer davetli kullanıcıysa, davet durumunu güncelle
            if (isInvited) {
                await inviteList.updateOne(
                    { email: username },
                    { 
                        $set: { 
                            status: 1, // Kayıt oldu
                            registeredAt: new Date(),
                            userId: userResult.insertedId
                        }
                    }
                );
            }

            if (isInvited) {
                // Davetli kullanıcılar için token oluştur
                const jwtPayload = {
                    id: userResult.insertedId.toString(),
                    email: username,
                    fullName: formattedName,
                    role: 'member',
                    clientId,
                    clientSchema,  // Eklenen alan
                    customerId: customerResult.insertedId
                };

                const token = await this.jwtx.sign({
                    payload: jwtPayload,
                    lifeTime: vars.token.tokenlifeTime
                });
                const refreshToken = await this.jwtx.sign({
                    payload: jwtPayload,
                    lifeTime: vars.token.refreshtokenLifeTime
                });

                // Refresh token'ı kaydet
                await this.saveToken({
                    dbConn,
                    payload: jwtPayload,
                    token: refreshToken,
                    saveLogin: true
                });

                resolve({
                    success: true,
                    message: "User created successfully. You can now login.",
                    userId: userResult.insertedId,
                    customerId: customerResult.insertedId,
                    isInvited,
                    token,
                    refreshToken,
                    user: {
                        id: userResult.insertedId,
                        email: username,
                        fullName: formattedName,
                        role: 'member'
                    }
                });
            } else {
                resolve({
                    success: true,
                    message: "User created successfully. Please check your email for verification.",
                    userId: userResult.insertedId,
                    customerId: customerResult.insertedId,
                    isInvited
                });
            }

        } catch (error) {
            console.error("Registration error:", error);
            reject({ message: "Internal server error: " + error.message });
        }
    });
};

const jwtx = exports.jwtx = {
    sign: (params) => {
        const {payload, lifeTime = 31556926} = params
        const key = process.env.JWT_KEY;
        const options = {
            expiresIn: "10s",
            audience: `${payload.id}`,
        };

        return new Promise(async (resolve, reject) => {
            delete payload.exp
            jwt.sign(
                payload,
                key,
                {
                    expiresIn: lifeTime, // 1 year in seconds
                },
                (err, token) => {
                    if(err) reject(err)
                    resolve(token)
                },
            );
        })
    },
}


exports.verifytoken = async (token, key) => {
    if (!key) {
      return { isValid: false, payload: null };
    }
  
    try {
      const decoded = jwt.verify(token, key);
      return {
        isValid: true,
        payload: decoded
      };
    } catch (error) {
      console.error('JWT verification error:', error);
      return { isValid: false, payload: null };
    }
  }

exports.decodeJwt = (token) => {
    try {
      return jwt.decode(token);
    } catch (error) {
      console.error('JWT decode error:', error);
      return null;
    }
  }

// exports.getUser = ({ dbConn, credentials }) => {
//     return new Promise(async (resolve) => {
//         const db = dbConn.db(dbSet.dbName)
//         const coll = db.collection(dbSet.tables.users);
//         const q = {userCode: credentials?.id}; 
//         const p = { projection: {_id: 0, password: 0, updatedAt: 0, createdAt: 0, token: 0 } };
//         var user = await coll.findOne(q, p); 
//         if (user) {
//             resolve(user);
//         } else {
//             resolve({user: false});
//         }
//     });
// }

// exports.login = ({ dbConn, credentials }) => {
//     return new Promise(async (resolve) => {
//         const username = credentials.username ? credentials.username.toLowerCase() : null;
//         const db = dbConn.db(dbSet.dbName)
//         const coll = db.collection(dbSet.tables.users);
//         if (username) {

//             const q = { $or: [{ email: username }, { phone: username }] }; //email: username
//             const p = { projection: {_id: 0, updatedAt: 0, createdAt: 0 } };
//             // var userAll = await coll.aggregate([]).toArray();
//             // console.log('p', q)
//             var user = await coll.findOne(q, p); 
//             // console.log('login q', dbSet.tables.users, q, user, userAll)
//             if (user) {
//                 var hashedPassword = user.password;
//                 var plainPassword = credentials.password
//                 bcrypt.compare(plainPassword, hashedPassword, async function (err, res) {
//                     delete user.password;
//                     if (err || !res) {
//                         try {
//                             var otpResults = await checkOTP({ user, plainPassword, dbConn })
//                             if (!otpResults) resolve({ data: false });
//                             if (otpResults) {
//                                 await deleteOTP({ otpResults, dbConn })
//                                 delete user.password
//                                 delete user.password_
//                                 resolve({ data: user })
//                             };
//                         } catch (e) {
//                             resolve({ data: false });
//                         }
//                         resolve({ data: false });
//                     }
//                     delete user.password
//                     delete user.password_
//                     resolve({data: user});
//                 });
//             } else {
//                 resolve({data: false});
//             }
//         } else {
//             resolve({data: false, error: 'username / password is needed.'});
//         }
//     });
// }
// // const deleteOTP = async props => {
// //     const { otpResults, dbConn } = props
// //     const db = dbConn.db(dbSet.dbName)
// //     const coll = db.collection(dbSet.tables.userotps);
// //     return new Promise(async (resolve, reject) => {
// //         var o_id = new ObjectId(otpResults?._id);
// //         try {
// //             await coll.updateOne({ _id: o_id }, { $set: { eop: new Date() } }, { upsert: true });
// //             resolve(true);
// //         } catch (e) {
// //             console.log('e', e)
// //             reject(false)
// //         }
// //     });
// // }

// // const checkOTP = async props => {
// //     const { user, dbConn, plainPassword } = props;
// //     const db = dbConn.db(dbSet.dbName)
// //     const coll = db.collection(dbSet.tables.userotps);

// //     return new Promise(async (resolve, reject) => {
// //         var q = { $or: [{ email: user.email }, { phone: user.phone }], eop: { $gte: new Date() }, status: 10 }
// //         try {
// //             var userOTP = await coll.findOne(q, {})
// //             if (userOTP) {
// //                 var { otp } = userOTP;
// //                 bcrypt.compare(plainPassword, otp, async function (err, res) {
// //                     if (err || !res) reject(false)
// //                     resolve(userOTP);
// //                 })
// //             } else {
// //                 reject(false)
// //             }
// //         } catch (e) {
// //             reject(false)
// //         }
// //     });
// // }

// exports.getRefreshToken = ({ dbConn, payload }) => {
//     const db = dbConn.db(dbSet.dbName)
//     const coll = db.collection(dbSet.tables.users);
//     const q = {userCode: payload.id}; 
//     const p = { projection: {_id: 0, token: 1 } };

//     return new Promise(async (resolve, reject) => {
//         try {
//             const token = await coll.findOne(q, p);
//             resolve(token);
//         } catch (e) {
//             reject(e);
//         }
//     })
// }

exports.saveToken = ({ dbConn, payload, token, saveLogin }) => {
    const db = dbConn.db(vars.db.dbName)
    const coll = db.collection(vars.db.collection.users);
    const q = {email: payload.email}; 

    return new Promise(async (resolve, reject) => {
        try {
            var dataU = {token}
            if (saveLogin) dataU.lastLogin = new Date(Date.now()) 
            await coll.updateOne(q,  { $set: dataU }, { upsert: true } );
            resolve(true);
        } catch (e) {
            reject(e);
        }
    })
}
// //https://github.com/TheWidlarzGroup/JWTAuthBackend/blob/main/src/Services/Token.Service.ts
// const jwtx = exports.jwtx = {
//     sign: (params) => {
//         const {payload, lifeTime = 31556926} = params
//         const key = process.env.JWT_KEY;
//         const options = {
//             expiresIn: "10s",
//             audience: `${payload.id}`,
//         };

//         return new Promise(async (resolve, reject) => {
//             // console.log('jwtx payload', payload)
//             delete payload.exp
//             jwt.sign(
//                 payload,
//                 key,
//                 {
//                     expiresIn: lifeTime, // 1 year in seconds
//                 },
//                 (err, token) => {
//                     /* Send succes with token */
//                     if(err) reject(err)
//                     resolve(token)
//                 },
//             );
//         })
//     },
// }

exports.verifyUser = ({ dbConn, token }) => {
    return new Promise(async (resolve, reject) => {
        try {
            const db = await dbConn.db(vars.db.dbName);
            const users = db.collection(vars.db.collection.users);

            const user = await users.findOne({ 
                verificationToken: token,
                verified: false
            });

            if (!user) {
                reject({ message: 'Invalid verification token' });
                return;
            }

            const updateResult = await users.updateOne(
                { _id: user._id },
                { 
                    $set: { 
                        verified: true,
                        verificationToken: null,
                        updatedAt: new Date()
                    }
                }
            );

            if (updateResult.modifiedCount !== 1) {
                reject({ message: 'Failed to verify user' });
                return;
            }

            const jwtPayload = {
                id: user._id.toString(),
                email: user.email,
                fullName: user.fullName,
                role: user.role,
                clientId: user.clientId, // Eklendi
                clientSchema: user.clientSchema,
                customerId: user.customerId
            };

            const jwtToken = jwt.sign(jwtPayload, process.env.JWT_SECRET, {
                expiresIn: '1d'
            });

            resolve({
                success: true,
                message: 'Email verification successful',
                token: jwtToken,
                user: {
                    id: user._id,
                    email: user.email,
                    fullName: user.fullName,
                    role: user.role
                }
            });

        } catch (error) {
            console.error('Verification error:', error);
            reject({ message: 'An error occurred during verification' });
        }
    });
};
