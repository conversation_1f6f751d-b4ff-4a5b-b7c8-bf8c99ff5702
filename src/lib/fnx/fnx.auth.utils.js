import bcrypt from 'bcrypt';
import clientPromise from '@/lib/db/mongodb';

import {vars} from '@/lib/constants';

const usersCollection = vars.db.collection.users;
const invitationsCollection = vars.db.collection.invitations;
const dbName = vars.db.dbName;
export async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

export async function hashPassword(password) {
  return await bcrypt.hash(password, 12);
}

export async function findUserByEmail(email) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  
  return await db.collection(usersCollection).findOne({ email });
}

export async function createUser(userData) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const user = {
    ...userData,
    createdAt: new Date(),
    updatedAt: new Date(),
    role: userData.role || 'member',
    isActive: true,
    onboardingCompleted: false,
  };
  
  const result = await db.collection(usersCollection).insertOne(user);
  return { ...user, _id: result.insertedId };
}

export async function checkInvitation(email) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  const q = {
    email,
    used: false,
    $or: [
      { status: { $exists: false } },
      { status: 'pending' }
    ],
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
    // status: 'pending',
    // expiresAt: { $gt: new Date() }
  };
  const invitation = await db.collection(invitationsCollection).findOne(q);
  console.log('inv check:', invitationsCollection, JSON.stringify(q), invitation);
  return invitation;
}

export async function markInvitationUsed(email) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  
  await db.collection(invitationsCollection).updateOne(
    { email, status: 'pending' },
    { 
      $set: { 
        status: 'used',
        usedAt: new Date()
      }
    }
  );
}

export async function updateUserRole(userId, role) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  
  await db.collection(usersCollection).updateOne(
    { _id: userId },
    { 
      $set: { 
        role,
        updatedAt: new Date()
      }
    }
  );
}
