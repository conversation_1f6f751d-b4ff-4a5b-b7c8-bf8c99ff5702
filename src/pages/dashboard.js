import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

import Layout from '@/components/layouts/Layout';

import ProtectedRoute from '../components/ProtectedRoute';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import {
  ChartBarIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,      // <-- Use this
  ShoppingBagIcon,
  EyeIcon,
  ArrowTrendingDownIcon     // <-- Use this
} from '@heroicons/react/24/outline';

export default function Dashboard() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);




  // console.log('Session in Dashboard:', session);
  useEffect(() => {

  if (status === 'unauthenticated') {
    // User is not authenticated, redirect to signin
    router.push('/auth/signin');
    return;
  }

    fetchAnalytics();
  }, []);

  useEffect(() => {
    // console.log('Session updated in Dashboard:', session, status)
    status !== 'loading' && console.log('Session updated in Dashboard with value:', session);
    // session && console.log('Session updated in Dashboard with value:', session);
  }, [session, status]);
  const fetchAnalytics = async () => {
    try {
      const response = await fetch('/api/dashboard/analytics');
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount || 0);
  };

  const StatCard = ({ title, value, icon: Icon, trend, trendValue, description }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend && (
          <div className="flex items-center text-xs text-muted-foreground">
            {trend === 'up' ? (
              <ArrowTrendingUpIcon className="h-3 w-3 text-green-500 mr-1" />   // <-- Use this
            ) : (
              <ArrowTrendingDownIcon className="h-3 w-3 text-red-500 mr-1" />
            )}
            <span className={trend === 'up' ? 'text-green-500' : 'text-red-500'}>
              {trendValue}
            </span>
            <span className="ml-1">{description}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );


  // if (status === 'loading') {
  //   return (
  //     <div className="min-h-screen flex items-center justify-center bg-gray-50">
  //       <div className="text-center">
  //         <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
  //         <p className="mt-4 text-gray-600">Authenticating...</p>
  //       </div>
  //     </div>
  //   );
  // }

  if (loading) {
    return (
      <ProtectedRoute>
        <Layout title="Dashboard">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout title="Dashboard">
        <div className="space-y-6">
          {/* Welcome Section */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
            <h1 className="text-2xl font-bold mb-2">
              Welcome back, {session?.user?.name}!
            </h1>
            <p className="text-blue-100">
              Here's what's happening with your sales today.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <StatCard
              title="Total Revenue"
              value={formatCurrency(analytics?.sales?.totalValue)}
              icon={CurrencyDollarIcon}
              trend="up"
              trendValue="+12.5%"
              description="from last month"
            />
            <StatCard
              title="Active Deals"
              value={analytics?.sales?.totalDeals || 0}
              icon={ChartBarIcon}
              trend="up"
              trendValue="+8.2%"
              description="from last month"
            />
            <StatCard
              title="Customers"
              value={analytics?.customers?.totalCustomers || 0}
              icon={UsersIcon}
              trend="up"
              trendValue="+5.1%"
              description="from last month"
            />
            <StatCard
              title="Win Rate"
              value={`${analytics?.sales?.winRate || 0}%`}
              icon={ArrowTrendingUpIcon} // <-- Use ArrowTrendingUpIcon here
              trend="up"
              trendValue="+2.3%"
              description="from last month"
            />
          </div>

          {/* Quick Actions */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common tasks to get you started
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <ChartBarIcon className="mr-2 h-4 w-4" />
                  Create New Sale
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <UsersIcon className="mr-2 h-4 w-4" />
                  Add Customer
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <ShoppingBagIcon className="mr-2 h-4 w-4" />
                  Add Product
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest updates from your team
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm">New deal created</p>
                      <p className="text-xs text-gray-500">2 minutes ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm">Customer updated</p>
                      <p className="text-xs text-gray-500">1 hour ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm">Deal moved to negotiation</p>
                      <p className="text-xs text-gray-500">3 hours ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>AI Insights</CardTitle>
                <CardDescription>
                  Powered by Google Gemini
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      Your conversion rate is 15% above average this month.
                    </p>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <p className="text-sm text-green-800">
                      Consider following up with 3 prospects from last week.
                    </p>
                  </div>
                  <Button className="w-full" variant="outline" size="sm">
                    <EyeIcon className="mr-2 h-4 w-4" />
                    View All Insights
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Chart Placeholder */}
          <Card>
            <CardHeader>
              <CardTitle>Sales Performance</CardTitle>
              <CardDescription>
                Revenue and deals over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Chart will be implemented with AI analytics</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
