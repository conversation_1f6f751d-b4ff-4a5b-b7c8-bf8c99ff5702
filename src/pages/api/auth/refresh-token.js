import clientPromise, { getCollection } from "@/lib/db/mongodb";
import { vars } from '@/lib/constants';
import * as fnxAuth from "@/lib/fnx/fnx.auth";
import { jwtDecode as jwt_decode } from 'jwt-decode';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ message: 'Refresh token is required' });
    }

    // Refresh token'ı decode et
    const decoded = jwt_decode(refreshToken);
    
    // Kullanıcıyı bul
    const dbConn = await clientPromise;
    const users = await getCollection(vars.db.collection.users);
    const user = await users.findOne({ 
      email: decoded.email,
      token: refreshToken // Kay<PERSON>ilen refresh token ile e<PERSON><PERSON>
    });

    if (!user) {
      console.log('no user', refreshToken, decoded.email);
      return res.status(401).json({ message: 'Invalid refresh token' });
    }

    // Yeni token'ları oluştur
    const jwtPayload = {
      id: user._id.toString(),
      email: user.email,
      fullName: user.fullName,
      custName: user.custName,
      customerId: user.customerId,
      clientId: user.clientId,
      clientSchema: user.clientSchema,
      role: user.role,
    };

    const newToken = await fnxAuth.jwtx.sign({payload: jwtPayload, lifeTime: vars.token.tokenlifeTime});
    return res.status(200).json({
      token: newToken,
      // refreshToken: newRefreshToken
    });

  } catch (error) {
    console.error('Refresh token error:', error);
    return res.status(500).json({ message: 'Error refreshing token' });
  }
}
