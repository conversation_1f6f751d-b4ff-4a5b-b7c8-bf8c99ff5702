import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { MongoDBAdapter } from "@next-auth/mongodb-adapter";
import clientPromise from "@/lib/db/mongodb";
import {
  verifyPassword,
  findUserByEmail,
  createUser,
  checkInvitation,
  markInvitationUsed
} from "@/lib/fnx/fnx.auth.utils";

import { jwtx } from "@/lib/fnx/fnx.auth";
import { vars } from '@/lib/constants';

// Temporary storage for tokens during sign-in process
const tokenStore = new Map();


export const authOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Find user in database
        const user = await findUserByEmail(credentials.email);
        // console.log('user:', credentials, user);
        if (!user) {
          return null;
        }

        // Verify password
        const isValid = await verifyPassword(credentials.password, user.password);
        if (!isValid) {
          return null;
        }
        // console.log('Credentials isValid:', credentials, isValid);
        // console.log('Credentials user:', user);
        return {
          id: user._id.toString(),
          email: user.email,
          name: user.name,
          role: user.role,
          // image: user.image,
        };
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      let existingUser;
      let userResult;
      existingUser = await findUserByEmail(user.email);
      let userId = existingUser?._id?.toString() || user?.id?.toString();
      console.log('userId:', userId);
      if (account.provider === "google") {
        if (!existingUser) {
          const invitation = await checkInvitation(user.email);
          if (!invitation) {
            return false; // Reject sign-in if no invitation
          }

          // Create new user
          userResult = await createUser({
            email: user.email,
            name: user.name,
            image: user.image,
            role: invitation.role || 'member',
            provider: 'google',
          });
          userId = userResult?.insertedId?.toString() || userId;
          await markInvitationUsed(user.email);
        }
      }

      let token;
      let refreshToken;
      try {
        const jwtPayload = {
          id: userId,
          email: existingUser?.email || user.email,
          fullName: existingUser?.name || user.name,
          role: existingUser?.role || 'member',
        };

        token = await jwtx.sign({
          payload: jwtPayload,
          lifeTime: vars.token.tokenlifeTime
        });
        refreshToken = await jwtx.sign({
          payload: jwtPayload,
          lifeTime: vars.token.refreshtokenLifeTime
        });

      } catch (error) {
        console.error('Error generating tokens:', error);
        return false; // Token generation failed, reject sign-in
      }

      // Store tokens in temporary storage with user ID as key
      const userKey = userId || user.id;
      tokenStore.set(userKey, { token, refreshToken });

      return true;
    },

    async jwt({ token, user, account }) {
      if (user) {
        const dbUser = await findUserByEmail(user.email);
        token.role = dbUser?.role || 'member';
        token.id = dbUser?._id?.toString() || user.id;
        token.onboardingCompleted = dbUser?.onboardingCompleted || false; // <-- EKLENDİ
      
        // Retrieve tokens from temporary storage
        const userKey = token.id;
        const storedTokens = tokenStore.get(userKey);
        if (storedTokens) {
          token.token = storedTokens.token;
          token.refreshToken = storedTokens.refreshToken;
          // Clean up temporary storage
          tokenStore.delete(userKey);
        }
      }
      return token;
    },
    async session({ session, token }) {
      session.user.role = token.role;
      session.user.id = token.id;
      session.user.onboardingCompleted = token.onboardingCompleted; // <-- EKLENDİ
      
      // Add token and refreshToken to session for both credentials and google providers
      if (token.token && token.refreshToken) {
        session.token = token.token;
        session.refreshToken = token.refreshToken;
      }
      
      return session;
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
};

export default NextAuth(authOptions);
